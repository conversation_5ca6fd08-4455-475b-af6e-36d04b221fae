import typing as _t
from openapi.spec import (
    OAS_DEFINITIONS,
    ParameterObject,
    RequestBodyObject,
    ResponseObject,
)


from functools import partial


def as_oas_path(path: str, /): ...


def as_django_path(path: str, /): ...


def _set_dict(
    data: dict, path: list[_t.<PERSON>], setter: _t.Callable[[_t.Any], _t.Any]
):
    d = data
    for index, key in enumerate(path):
        if index == len(path) - 1:
            d[key] = setter(d.get(key))
        else:
            d = d.setdefault(key, {})
    return data


def _set_response_schema(method, new, old):
    """OAS 设置 response schema 可能会冲突。

    这是个临时检查手段，后期考虑使用 merge 方式，如果遇到无法合并的问题再报错。
    """
    if old is not None and old != new:
        raise RuntimeError(f"Conflicting response schema for {method}", old, new)
    return new


_HTTP_METHODS = [
    "get",
    "post",
    "put",
    "delete",
    "patch",
    "head",
    "options",
    "trace",
]


def as_path_item_object(obj, /) -> dict:
    rv = {}

    for method in _HTTP_METHODS:
        if not hasattr(obj, method):
            continue

        method_handle = getattr(obj, method)
        method_definitions = getattr(method_handle, OAS_DEFINITIONS, [])
        for definition in reversed(method_definitions):
            if isinstance(definition, ParameterObject):
                _set_dict(
                    rv,
                    [method, "parameters"],
                    lambda x: (x or []) + [definition.spec],
                )
            elif isinstance(definition, ResponseObject):
                _set_dict(
                    rv,
                    [method, "responses", str(definition.status_code)],
                    partial(_set_response_schema, method_handle, definition.spec()),
                )
            elif isinstance(definition, RequestBodyObject):
                _set_dict(rv, [method, "requestBody"], lambda _: definition.spec)
            else:
                assert isinstance(definition, dict), definition
                _set_dict(
                    rv,
                    [method],
                    lambda x: {**x, **definition} if x is not None else definition,
                )

    return rv
